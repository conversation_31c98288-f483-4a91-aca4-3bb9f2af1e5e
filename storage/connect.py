# Import necessary modules
from minio import <PERSON>o
import os
from minio.error import S3<PERSON>rror
import json
import requests
from io import BytesIO

# Get the current directory
current_directory = os.path.dirname(os.path.abspath(__file__))

# Initialize the MinIO client with the correct endpoint and port
client = Minio(
    endpoint="contentmatch1.nyc3.digitaloceanspaces.com",
    access_key="DO00Y9H3LET6XKV3WYA4",
    secret_key="sa/S75K3QGnabJ1eZ1ks2SM57LVPk7eyD2mrb8mQY9Y"
)

# Print a message if the connection is successfully established
if client:
    print("Successfully connected to the MinIO server")

# Function to upload a file to the MinIO server
def upload_file(file_url, file_name):
    # Create a new bucket named 'reviews'
    client.make_bucket('reviews',object_lock=False)
    try:
        # Get the file from the provided URL
        file = requests.get(file_url)
        temp_dir_name = './temp'
        temp_file = f'{temp_dir_name}/{file_name}'
        # Create a temporary directory if it doesn't exist
        if not os.path.exists(temp_dir_name):
            os.makedirs(temp_dir_name)
        # Write the content of the file to a temporary file
        with open(temp_file, 'wb') as f:
            f.write(file.content)
        temp_file_path = os.path.abspath(temp_file)
        # Upload the temporary file to the MinIO server
        screenshot = client.fput_object(
            "reviews",
            f"screenshots/{file_name}",
            temp_file_path,
        )
        print("Upload response:", screenshot)
        return screenshot
    except Exception as e:
        return None

# Function to get a file from the MinIO server
def get_file(file_name):
    # Create a new bucket named 'reviews'
    client.make_bucket('reviews',object_lock=False)
    print(f"Getting file {file_name}")
    try:
        # Get the file from the MinIO server
        data = client.get_object(bucket_name="reviews", object_name=f"screenshots/{file_name}")
        # Write the content of the file to a local file
        with open(f"./temp/{file_name}", "wb") as file_data:
            for d in data.stream(32*1024):
                file_data.write(d)
        return True
    except Exception as e:
        print(e)
        return None
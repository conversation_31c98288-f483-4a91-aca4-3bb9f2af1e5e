import discord
from discord.ext import commands

from src.config import config

from functions.startups import startup
from functions.get_prefix import get_prefix

bot = commands.AutoShardedBot(
    command_prefix=get_prefix,
    intents=discord.Intents.all(),
    case_insensitive=True,
    help_command=None
)

@bot.event
async def on_ready():
    print('-'*40)
    print(f"{bot.user.name} is ready to work...")
    print(f"Bot ID: {bot.user.id}")

    print('-'*40)
    for command in bot.commands:
        print(f"Command: {command.name} loaded successfully.")
    print('-'*40)
    await startup(bot)

import asyncio
async def activity(bot):
    while True:
        try:
            await bot.change_presence(activity=discord.Game(name=f"{config.prefix}help | {len(bot.guilds)} servers"))
            await asyncio.sleep(60)
            await bot.change_presence(activity=discord.Game(name=f"{config.prefix}help | {len(bot.users)} users"))
            await asyncio.sleep(60)
        except:
            await asyncio.sleep(10)


from commands.setup import setup as setup_command
@bot.command(help="Setup the bot")
async def setup(ctx:commands.Context):
    await setup_command(ctx,bot)

from commands.deletecontent import deletecontent as deletecontent_command
@bot.command(help="Delete a content by content_id",aliases=["delcontent"])
async def deletecontent(ctx:commands.Context,content_id:int):
    await deletecontent_command(ctx,content_id)

from commands.listcontent import listcontent as listcontent_command
@bot.command(help="List all content")
async def listcontent(ctx):
    await listcontent_command(ctx,bot)

from commands.reload import reload as reload_command
@bot.command(help="Reload the bot")
async def reload(ctx):
    await reload_command(ctx,bot)

from commands.settings import settings as settings_command
@bot.command(help="Show bot settings")
async def settings(ctx):
    await settings_command(ctx,bot)

from commands.banuser import banuser as banuser_command
@bot.command(help="Ban a user")
async def banuser(ctx,user:discord.User=None,reason:str=None):
    await banuser_command(ctx,user,reason)

from commands.unbanuser import unbanuser as unbanuser_command
@bot.command(help="Unban a user")
async def unbanuser(ctx,user:discord.User=None):
    await unbanuser_command(ctx,user)

from commands.help import help as help_command
@bot.command(help="Show bot help")
async def help(ctx):
    await help_command(ctx,bot)

@bot.event
async def on_message(message):
    if message.author.bot:
        return
    if not message.guild:
        return
    await bot.process_commands(message)

@bot.command(help="Show bot latency.")
async def ping(ctx):
    await ctx.send(f"Pong! {round(bot.latency*1000)}ms")

from commands.info import info as info_command
@bot.command(help="Show bot information")
async def info(ctx):
    await info_command(ctx,bot)


from commands.stats import stats as stats_command
@bot.command()
async def stats(ctx):
    await stats_command(ctx,bot)




bot.run(config.token, reconnect=True)()
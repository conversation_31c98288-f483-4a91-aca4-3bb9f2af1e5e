import psycopg2
from psycopg2 import sql, pool, extras
from psycopg2.extras import execute_batch
from src import auth
from functions import loadjson
from database.connect import connection_pool
import json


def create_table(name):
    
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute(f"""CREATE TABLE IF NOT EXISTS {name} (
    id SERIAL PRIMARY KEY,
    user_id BIGINT UNIQUE,
    data JSON
    )""")
    
    connection.commit()
    connection_pool.putconn(connection)

create_table('users')

def get_user_data(user_id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute("SELECT * FROM users WHERE user_id = %s", (user_id,))
    user = cursor.fetchall()
    if user:
        user = user[0][2]
    else:
        user = None

    
    connection_pool.putconn(connection)
    
    return user

#get all users as json
def get_all_users():
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute("SELECT * FROM users")
    users = cursor.fetchall()
    
    connection_pool.putconn(connection)
    
    return users

def insert_user_data(user_id, data):
    table_name = 'users'

    connection = connection_pool.getconn()

    try:
        with connection.cursor(cursor_factory=extras.DictCursor) as db:
            query = f"INSERT INTO {table_name} (user_id, data) VALUES (%s, %s::JSON) ON CONFLICT (user_id) DO UPDATE SET data = %s::JSON"
            values = (user_id, json.dumps(data), json.dumps(data))
            db.execute(query, values)
            connection.commit()
    finally:
        connection_pool.putconn(connection)

def delete_user_data(user_id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute("DELETE FROM users WHERE user_id = %s", (user_id,))
    
    connection.commit()
    connection_pool.putconn(connection)
import psycopg2
from psycopg2 import sql, pool, extras
from psycopg2.extras import execute_batch
from src import auth
from functions import loadjson
from database.connect import connection_pool
import json

table_name = "contents"

def create_table(name):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute(f"""CREATE TABLE IF NOT EXISTS {name} (
    id SERIAL PRIMARY KEY,
    publisher BIGINT,
    title TEXT,
    description TEXT,
    link TEXT,
    guild_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviews JSON,
    reports JSON
    )""")
    
    connection.commit()
    connection_pool.putconn(connection)

create_table(table_name)

def get_content_data_by_id(id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("SELECT * FROM contents WHERE id = %s", (id,))
        content = cursor.fetchall()
        if content:
            return content[0]
        else:
            return None
    finally:
        connection_pool.putconn(connection)

def get_content_data_by_publisher(publisher):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("SELECT * FROM contents WHERE publisher = %s", (publisher,))
        content = cursor.fetchall()
        if content:
            return content
        else:
            return None
    
    finally:
        connection_pool.putconn(connection)

def get_all_content_data():
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("SELECT * FROM contents")
        contents = cursor.fetchall()
        return contents
    finally:
        connection_pool.putconn(connection)

def get_a_random_content(blacklist_ids=[]):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        if len(blacklist_ids) == 0:
            cursor.execute("SELECT * FROM contents ORDER BY RANDOM() LIMIT 1")
        else:
            cursor.execute("SELECT * FROM contents WHERE id NOT IN %s ORDER BY RANDOM() LIMIT 1", (tuple(blacklist_ids),))
        content = cursor.fetchall()
        if content:
            return content[0]
        else:
            return None
    finally:
        connection_pool.putconn(connection)


def insert_content_data(publisher, title, description, link, guild_id, reviews={}, reports={}):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("INSERT INTO contents (publisher, title, description, link, guild_id, reviews, reports) VALUES (%s, %s, %s, %s, %s, %s::JSON, %s::JSON) RETURNING id", 
                       (publisher, title, description, link, guild_id, json.dumps(reviews), json.dumps(reports)))
        connection.commit()
        inserted_id = cursor.fetchone()[0]
        return inserted_id
    finally:
        connection_pool.putconn(connection)
def check_content_by_link(link,id=None):
    # link = json.dumps(link)
    connection = connection_pool.getconn()
    cursor = connection.cursor()

    try:
        if id:
            cursor.execute("SELECT * FROM contents WHERE link = %s", (link,))
            content = cursor.fetchall()
            print(content)
            if len(content) != 0:
                if content[0][0] == id:
                    print('true')
                    return False
                else:
                    return True
            else:
                return False
        else:
            cursor.execute("SELECT * FROM contents WHERE link = %s", (link,))
            content = cursor.fetchall()
            if content:
                return content[0]
            else:
                return None
    finally:
        connection_pool.putconn(connection)



def update_content_reviews(id, reviews):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("UPDATE contents SET reviews = %s::JSON WHERE id = %s", (json.dumps(reviews), id))
        connection.commit()
    finally:
        connection_pool.putconn(connection)

def update_content_reports(id, reports):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("UPDATE contents SET reports = %s::JSON WHERE id = %s", (json.dumps(reports), id))
        connection.commit()
    finally:
        connection_pool.putconn(connection)

def delete_content_data(id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("DELETE FROM contents WHERE id = %s", (id,))
        connection.commit()
    finally:
        connection_pool.putconn(connection)

def get_all_content_by_page(limit_per_page=10,guild_id=None,publisher=None):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    try:
        if guild_id:
            cursor.execute("SELECT * FROM contents WHERE guild_id = %s", (guild_id,))
        elif publisher:
            cursor.execute("SELECT * FROM contents WHERE publisher = %s", (publisher,))
        else:
            cursor.execute("SELECT * FROM contents")

        contents = cursor.fetchall()

        # Split contents into groups of 20
        content_groups = [contents[i:i+limit_per_page] for i in range(0, len(contents), limit_per_page)]
        
        return content_groups
    finally:
        connection_pool.putconn(connection)

def update_content_data(id, title=None, description=None, link=None):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        # update content by id
        query = "UPDATE contents SET (title, description, link) = (%s, %s, %s) WHERE id = %s RETURNING id"
        values = (title, description, link, id)
        res=cursor.execute(query, values)
        connection.commit()

        return res
    finally:
        connection_pool.putconn(connection)

def get_all_reviews_by_publisher(publisher,page_limit=2):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("SELECT * FROM contents WHERE publisher = %s", (publisher,))
        contents_data = cursor.fetchall()
        
        reviews = []

        for content in contents_data:
            if len(content[7]) == 0:
                continue
            for user_id,review in content[7].items():
                data = {
                    "id": content[0],
                    "publisher": content[1],
                    "title": content[2],
                    "description": content[3],
                    "link": content[4],
                    "guild_id": content[5],
                    "created_at": content[6],
                    "user": user_id,
                    "review": review
                }
                reviews.append(data)
        
        sorted_as_pages = [reviews[i:i+page_limit] for i in range(0, len(reviews), page_limit)]

        
        return sorted_as_pages
    finally:
        connection_pool.putconn(connection)
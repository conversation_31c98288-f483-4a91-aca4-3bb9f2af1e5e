import psycopg2
from psycopg2 import sql, pool, extras
from psycopg2.extras import execute_batch
from src import auth
from functions import loadjson
from database.connect import connection_pool
import json

def create_table(name):
    
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute(f"""CREATE TABLE IF NOT EXISTS {name} (
    id SERIAL PRIMARY KEY,
    guild_id BIGINT UNIQUE,
    data JSON
    )""")
    
    connection.commit()
    connection_pool.putconn(connection)

create_table('guilds')

def get_guild_data(guild_id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute("SELECT * FROM guilds WHERE guild_id = %s", (guild_id,))
    guild = cursor.fetchall()
    if guild:
        return guild[0][2]
    else:
        return None
    
    connection_pool.putconn(connection)
    

#get all guilds as json

def get_all_guilds():
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute("SELECT * FROM guilds")
    guilds = cursor.fetchall()
    
    connection_pool.putconn(connection)
    
    json_guilds = {}
    for guild in guilds:
        json_guilds[guild[1]] = guild[2]
    return json_guilds

def insert_guild_data(guild_id, data):
    table_name = 'guilds'

    connection = connection_pool.getconn()

    try:
        with connection.cursor(cursor_factory=extras.DictCursor) as db:
            query = f"INSERT INTO {table_name} (guild_id, data) VALUES (%s, %s::JSON) ON CONFLICT (guild_id) DO UPDATE SET data = %s::JSON"
            values = (guild_id, json.dumps(data), json.dumps(data))
            db.execute(query, values)
            connection.commit()
            print('inserted')
    finally:
        connection_pool.putconn(connection)
        download_all_guild_prefix_data()

def delete_guild_data(guild_id):
    try:
        connection = connection_pool.getconn()
        cursor = connection.cursor()
        
        cursor.execute("DELETE FROM guilds WHERE guild_id = %s", (guild_id,))
        connection.commit()
        
        return True
    finally:
        connection_pool.putconn(connection)
        download_all_guild_prefix_data()


def download_all_guild_prefix_data():
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute("SELECT * FROM guilds")
    guilds_data = cursor.fetchall()
    
    connection_pool.putconn(connection)

    print(guilds_data)

    data = {}
    for guild in guilds_data:
        data[str(guild[1])] = guild[2]['prefix']
    from cache.prefix import save_prefix_data, prefix_data
    save_prefix_data(data)
    prefix_data = data
    print(prefix_data)
    return data
    
    
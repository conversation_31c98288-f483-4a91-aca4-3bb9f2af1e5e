import psycopg2
from psycopg2 import sql, pool, extras
from psycopg2.extras import execute_batch
from src import auth
from functions import loadjson
from database.connect import connection_pool
import json
import time

def get_latency_in_ms():
    start = time.time()
    connection = connection_pool.getconn()
    cursor = connection.cursor()

    
    cursor.execute("SELECT * FROM guilds")
    end = time.time()

    connection_pool.putconn(connection)

    return round((end - start) * 1000, 2)
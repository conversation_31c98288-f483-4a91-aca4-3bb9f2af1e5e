import psycopg2
from psycopg2 import sql, pool, extras
from psycopg2.extras import execute_batch
from src import auth
from functions import loadjson
from database.connect import connection_pool
import json

table_name = "bans"

def create_table(name):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    cursor.execute(f"""CREATE TABLE IF NOT EXISTS {name} (
    id SERIAL PRIMARY KEY,
    user_id BIGINT,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )""")
    
    connection.commit()
    connection_pool.putconn(connection)

create_table(table_name)

def get_ban_data_by_id(id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("SELECT * FROM bans WHERE id = %s", (id,))
        ban = cursor.fetchall()
        if ban:
            return ban[0]
        else:
            return None
    finally:
        connection_pool.putconn(connection)

def get_ban_data_by_user_id(user_id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    try:
        cursor.execute("SELECT * FROM bans WHERE user_id = %s", (user_id,))
        ban = cursor.fetchall()
        if ban:
            return ban
        else:
            return None
    
    finally:
        connection_pool.putconn(connection)

def ban_user(user_id, reason):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    quiery = f"""INSERT INTO bans (user_id, reason) VALUES (%s, %s) ON CONFILCT (user_id) DO UPDATE SET reason = %s"""
    cursor.execute(quiery, (user_id, reason, reason))
    
    connection.commit()
    connection_pool.putconn(connection)


def unban_user(user_id):
    connection = connection_pool.getconn()
    cursor = connection.cursor()
    
    quiery = f"""DELETE FROM bans WHERE user_id = %s"""
    cursor.execute(quiery, (user_id,))
    
    connection.commit()
    connection_pool.putconn(connection)
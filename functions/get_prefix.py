from src.config import config

def get_prefix(bot,message):
    
    if not message.guild:
        prefixes = config.prefix, f"<@!{bot.user.id}>", f"<@{bot.user.id}> ", f"<@!{bot.user.id}> ", f"<@{bot.user.id}> "
    
    else:
        from cache.prefix import prefix_data
        if str(message.guild.id) not in prefix_data:
            prefix_data[str(message.guild.id)] = config.prefix
        prefixes = prefix_data.get(str(message.guild.id)),config.prefix, f"<@!{bot.user.id}>", f"<@{bot.user.id}> ", f"<@!{bot.user.id}> ", f"<@{bot.user.id}> "
    return prefixes
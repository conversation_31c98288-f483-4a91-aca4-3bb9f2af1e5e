from functions import create_data
from src.config import config
import re

from database import guilds,bans

def check_perms(ctx):
    guild_data = guilds.get_guild_data(ctx.guild.id)
    if not guild_data:
        guild_data = create_data.create_guild_data(ctx.guild.id)
    
    if ctx.author.id in guild_data.get('admin_ids'):
        return True
    elif ctx.author.id == ctx.guild.owner.id:
        return True
    else:
        return False

def check_bot_owner(user_id):
    if user_id in config.owner_ids:
        return True
    elif user_id in config.admin_ids:
        return True
    else:
        return False

def check_ban(user_id):
    ban_data = bans.get_ban_data_by_user_id(user_id)
    if ban_data:
        return True
    else:
        return False

def is_valid_content_link(link):
    # List of allowed content platforms
    allowed_platforms = ["youtube.com", "youtu.be", "instagram.com", "tiktok.com"]

    # Regular expression pattern for matching YouTube content IDs
    youtube_id_pattern = re.compile(r'(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})')

    # Extract domain from the link
    domain = link.split("/")[2]
    domain = domain.replace("www.", "")

    # Check if the domain is in the list of allowed platforms
    if domain not in allowed_platforms:
        return False

    # Extract content ID from the lin
    # All checks passed, link is valid
    return True
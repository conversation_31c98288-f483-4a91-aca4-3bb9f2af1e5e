import json
from functions import loadjson,writejson

from database.connect import connection_pool
from database import users,guilds

from src.config import config

def create_guild_data(guild_id):
    data = {
        "id": guild_id,
        "prefix": config.prefix,
        "admin_ids": [],
        "main_menu_channel": None,
        "main_menu_message": None,
        "report_channel": None,
        "moderator_role": None
    }
    guilds.insert_guild_data(guild_id, data)
    return data

def create_user_data(user_id):
    data = {
        "id": user_id,
        "admin": False,
        "moderator": False,
        "review_ids": [],
        "post_ids": [],
        "report_ids": []
    }
    users.insert_user_data(user_id, data)
    return data
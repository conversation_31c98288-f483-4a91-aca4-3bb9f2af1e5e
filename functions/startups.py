import discord
from discord.ext import commands
import asyncio
import time
import re
import requests
from functions import create_data
from data import emojis
from src import color
from database import guilds,users,contents
import random
from PIL import Image

from functions.checks import check_perms,check_bot_owner,check_ban,is_valid_content_link


async def startup(bot):
    guilds.download_all_guild_prefix_data()
    await resume_all_menus(bot)


menue_image_url = "https://cdn.discordapp.com/attachments/1201476250397581423/1210600374600339497/image0.jpg?ex=65eb2675&is=65d8b175&hm=93f2b1cbddef377e301059144b7b101ffe3514eadc5ddc55d05c310e82771934&"


ss_storage_channels = [1210651433901228092,1210652611355877476]


async def resume_all_menus(bot):
    all_guild_data = guilds.get_all_guilds()
    print(f"\n\n{all_guild_data}\n\n")
    for guild_id,guild_data in all_guild_data.items():
        guild_id = guild_data.get('id')
        print(f"Resuming main menu for guild {guild_id}")
        await start_main_menu(bot,guild_id)


post_timeout_users = {}
async def start_main_menu(bot,guild_id,forced=False):
    guild_data = guilds.get_guild_data(guild_id)
    if not guild_data:
        guild_data = create_data.create_guild_data(guild_id)
    
    main_menu_channel = guild_data.get('main_menu_channel')
    if not main_menu_channel:
        return
    
    main_menu_channel = await bot.fetch_channel(main_menu_channel)
    if not main_menu_channel:
        return
    view = discord.ui.View(timeout=None)

    embed = discord.Embed(
        title="Content Menu",
        color=color.purple
    )
    embed.add_field(
        name=f"ContentMatch FAQ",
        value=f"The ContentMatch bot allows users from your discord server and other servers, to engage and help grow your content.",
        inline=False
    )
    embed.add_field(
        name=f"Our Forums",
        value=f"Our forum is a wealth of knowledge, to help creators grow. You can visit it at\n[[**Forum Link!**](https://forums.contentmatch.io/)]",
        inline=True
    )
    embed.add_field(
        name=f"Our Main App",
        value=f"This is where creators, communities and brands go to, to increase their engagement. It is like an app you haven't used before, due to it's innovative algorithm.\n[[**App Link!**](https://app.contentmatch.io/)]",
        inline=True
    )

    embed.set_image(url=menue_image_url)


    
    embed.set_footer(text="This is a dummy text. you can add whatevery you want.",icon_url=bot.user.display_avatar.url)
    embed.set_thumbnail(url=bot.user.display_avatar.url)

    play_content_button = discord.ui.Button(
        style=discord.ButtonStyle.primary,
        label="Play content",
        emoji=emojis.content
    )
    play_content_button.callback = lambda i: play_content(i)
    

    post_button = discord.ui.Button(
        style=discord.ButtonStyle.primary,
        label="Post",
        emoji=emojis.post
    )
    post_button.callback = lambda i: post_button_callback(i)
    

    async def post_button_callback(interaction:discord.Interaction):
        if check_ban(interaction.user.id):
            return await interaction.response.send_message(content=f"You are banned from using this bot.",)
        
        user_data = users.get_user_data(interaction.user.id)
        if not user_data:
            user_data = create_data.create_user_data(interaction.user.id)
        
        if not check_bot_owner(interaction.user.id) and str(interaction.user.id) in post_timeout_users:
            if time.time() - post_timeout_users[str(interaction.user.id)] < 3600:
                return await interaction.response.send_message(content=f"**You are posting too fast. You can post again in <t:{int(time.time() + 3600)}:R>.**",ephemeral=True)

        class post_modal(discord.ui.Modal,title="Submit Post"):
            

            post_title = discord.ui.TextInput(
                label="Post Title",
                placeholder="Title...",
                required=True,
                style=discord.TextStyle.short,
                row=1
            )

            description = discord.ui.TextInput(
                label="Post Description",
                placeholder="Description...",
                style=discord.TextStyle.long,
                required=False,
                row=2
            )
            link = discord.ui.TextInput(
                label="Link",
                placeholder="Link...",
                required=True,
                style=discord.TextStyle.short,
                row=3
            )
            
            async def on_submit(self,interaction:discord.Interaction):
                title = self.post_title.value
                description = self.description.value
                link = self.link.value
            
                # check the link is valid or not it must be link like youtube content or insta or ticktok
                await interaction.response.defer(thinking=True,ephemeral=True)
                if not is_valid_content_link(link):
                    return await interaction.edit_original_response(content="Invalid link.")

                if contents.check_content_by_link(link):
                    return await interaction.edit_original_response(content=f"{emojis.error} This content is already posted.")
                
                content_id = contents.insert_content_data(
                    publisher=interaction.user.id,
                    title=title,
                    description=description,
                    link=link,
                    guild_id=interaction.guild.id,
                    reviews={},
                    reports={}
                )

                if not content_id:
                    return await interaction.edit_original_response(content="Failed to submit post.")
                
                user_data = users.get_user_data(interaction.user.id)
                if not user_data:
                    user_data = create_data.create_user_data(interaction.user.id)
                
                user_post_ids = user_data.get('post_ids')

                user_post_ids.append(content_id)
                user_data['post_ids'] = user_post_ids
                users.insert_user_data(interaction.user.id, user_data)
                await interaction.edit_original_response(content="Post submitted successfully.")
                global post_timeout_users
                post_timeout_users[str(interaction.user.id)] = time.time()

        await interaction.response.send_modal(post_modal())

    async def play_content(interaction:discord.Interaction):
        await interaction.response.defer(ephemeral=True,thinking=True)
        if check_ban(interaction.user.id):
            return await interaction.edit_original_response(content=f"You are banned from using this bot.")
        user_data = users.get_user_data(interaction.user.id)
        if not user_data:
            user_data = create_data.create_user_data(interaction.user.id)
        
        blacklisted_ids = []
        for i in user_data.get('review_ids'):
            blacklisted_ids.append(i)
        for i in user_data.get('post_ids'):
            blacklisted_ids.append(i)
        for i in user_data.get('report_ids'):
            blacklisted_ids.append(i)
        
        temp_blacklisted_ids = []


        get_a_random_content = contents.get_a_random_content(blacklisted_ids)
        if not get_a_random_content:
            return await interaction.edit_original_response(content=f"No content found.")
        embed = discord.Embed(
            title=get_a_random_content[2],
            description=f"{get_a_random_content[3]}",
            color=color.green,
            url=get_a_random_content[4]
        )
        embed.description += f"\n\n[**[Link]**]({get_a_random_content[4]})"
        embed.description += f"\n**Publisher:** <@{get_a_random_content[1]}>"
        embed.description += f"\n**Published:** <t:{int(get_a_random_content[6].timestamp())}>"

        embed.set_thumbnail(url=bot.user.display_avatar.url)
        if 'youtube.com' in get_a_random_content[4]:
            content_id = re.search(r'(?<=v=)[^&]+', get_a_random_content[4])
            content_id = content_id.group(0) if content_id else None
            embed.set_image(url=f"https://img.youtube.com/vi/{content_id}/maxresdefault.jpg")
        elif 'instagram.com' in get_a_random_content[4]:
            embed.set_image(url=f"https://instagram.com/p/{get_a_random_content[4].split('/')[-1]}/media/?size=l")
        elif 'tiktok.com' in get_a_random_content[4]:
            embed.set_image(url=f"https://tiktok.com/p/{get_a_random_content[4].split('/')[-1]}/media/?size=l")
        else:
            embed.set_image(url=get_a_random_content[4])
        published_datetime = get_a_random_content[6]
        published_datetime = published_datetime.strftime("%Y-%m-%d %H:%M:%S UTC")
        embed.set_footer(text=f"Content ID: {get_a_random_content[0]}")

        view = discord.ui.View(timeout=None)
        user = interaction.user

        options = []
        for i in range(1,6):
            options.append(
                discord.SelectOption(
                    label=str(i),
                    value=str(i),
                    description=f"Rate {i} out of 5 stars.",
                    emoji=emojis.star
                )
            )
        
        open_in_browser = discord.ui.Button(
            style=discord.ButtonStyle.link,
            label="In Browser",
            url=get_a_random_content[4],
            emoji=emojis.browser,
            row=1
        )
        report_button = discord.ui.Button(
            style=discord.ButtonStyle.danger,
            label="Report",
            emoji=emojis.report,
            row=1
        )
        
        report_button.callback = lambda i: report_button_callback(i)

        async def report_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content="You can't report your own summoned content.", ephemeral=True)
            
            class report_content(discord.ui.Modal,title="Report Content"):
                reason = discord.ui.TextInput(
                    label="Reason",
                    placeholder="Please write a reason for reporting the content...",
                    required=True,
                    style=discord.TextStyle.long
                )
                async def on_submit(self,interaction:discord.Interaction):
                
                    await interaction.response.defer(ephemeral=True,thinking=True)
                    if check_ban(interaction.user.id):
                        return await interaction.edit_original_response(content=f"You are banned from using this bot.")
                    
                    
                    reason = self.reason.value
                    content_id = get_a_random_content[0]
                    user_data = users.get_user_data(interaction.user.id)
                    if not user_data:
                        user_data = create_data.create_user_data(interaction.user.id)
                    content_data = contents.get_content_data_by_id(content_id)
                    if not content_data:
                        return await interaction.edit_original_response(content="Content not found.")
                    
                    reports = content_data[8]
                    if str(interaction.user.id) in reports:
                        await interaction.edit_original_response(content="You have already reported this content.")
                        try:
                            await content_message.delete()
                        except:
                            print("Failed to delete the message.")
                        return
                    
                    reports[interaction.user.id] = {
                        "reason": reason
                    }
                    contents.update_content_reports(
                        id=int(content_id),
                        reports=reports
                    )
                    user_report_ids = user_data.get('report_ids') if len(user_data.get('report_ids')) !=0 else []
                    user_report_ids.append(content_id)
                    user_data['report_ids'] = user_report_ids
                    users.insert_user_data(interaction.user.id, user_data)
                    embed = discord.Embed(
                        title="Report Submitted",
                        description="Report submitted successfully.",
                        color=color.green
                    )
                    embed.set_thumbnail(url=bot.user.display_avatar.url)
                    embed.set_footer(text="Report submitted successfully.")
                    await interaction.edit_original_response(embed=embed, view=None,content=None)
                    async def send_report_log(guild_id,author,content_id,reason):
                        guild_data = guilds.get_guild_data(guild_id)
                        if not guild_data:
                            return
                        report_channel = guild_data.get('report_channel')
                        if not report_channel:
                            return
                        report_channel = bot.get_channel(report_channel)
                        if not report_channel:
                            return
                        content_data = contents.get_content_data_by_id(content_id)
                        
                        embed = discord.Embed(
                            title="Content Reported",
                            description=f"Content reported by <@{author}>",
                            color=color.red
                        )
                        embed.add_field(
                            name="Content ID",
                            value=f"```\n{content_id}```",
                            inline=False
                        )
                        embed.add_field(
                            name="Reason",
                            value=f"```\n{reason}```",
                            inline=False
                        )
                        embed.add_field(
                            name="Publisher",
                            value=f"<@{content_data[1]}>",
                            inline=False
                        )
                        embed.add_field(
                            name="Published",
                            value=f"<t:{int(content_data[6].timestamp())}>",
                            inline=False
                        )
                        embed.add_field(
                            name="Link",
                            value=f"```\n{content_data[4]}```",
                            inline=False
                        )
                        embed.set_thumbnail(url=bot.user.display_avatar.url)
                        embed.set_footer(text="Content Reported")
                        await report_channel.send(embed=embed)
                    await send_report_log(interaction.guild.id,interaction.user.id,content_id,reason)


                    # DELETE THE old interaction where the content is
                    try:
                        await content_message.delete()
                    except:
                        print("Failed to delete the message.")

            await interaction.response.send_modal(report_content())

        next_button = discord.ui.Button(
            style=discord.ButtonStyle.primary,
            label="Next",
            emoji=emojis.arrow,
            row=1
        )
        view.add_item(next_button)
        next_button.callback = lambda i: next_button_callback(i)

        async def next_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
            
            await interaction.response.edit_message(content="Loading...",view=None,embed=None)
            user_data = users.get_user_data(user.id)
            nonlocal temp_blacklisted_ids,get_a_random_content,embed
            temp_blacklisted_ids.append(get_a_random_content[0])
            blacklisted_ids = []
            for i in user_data.get('review_ids'):
                blacklisted_ids.append(i)
            for i in user_data.get('post_ids'):
                blacklisted_ids.append(i)
            for i in user_data.get('report_ids'):
                blacklisted_ids.append(i)
            for i in temp_blacklisted_ids:
                blacklisted_ids.append(i)
            get_a_random_content = contents.get_a_random_content(blacklisted_ids)
            if not get_a_random_content:
                embed = discord.Embed(
                    title="No Content Found",
                    description="No content found.",
                    color=color.red
                )
                return await interaction.edit_original_response(embed=embed, view=None)
            embed = discord.Embed(
                title=get_a_random_content[2],
                description=f"{get_a_random_content[3]}",
                color=color.green,
                url=get_a_random_content[4]
            )
            embed.description += f"\n\n[**[Link]**]({get_a_random_content[4]})"
            embed.description += f"\n**Publisher:** <@{get_a_random_content[1]}>"
            embed.description += f"\n**Published:** <t:{int(get_a_random_content[6].timestamp())}>"

            embed.set_thumbnail(url=bot.user.display_avatar.url)
            if 'youtube.com' in get_a_random_content[4]:
                content_id = re.search(r'(?<=v=)[^&]+', get_a_random_content[4])
                content_id = content_id.group(0) if content_id else None
                embed.set_image(url=f"https://img.youtube.com/vi/{content_id}/maxresdefault.jpg")
            elif 'instagram.com' in get_a_random_content[4]:
                embed.set_image(url=f"https://instagram.com/p/{get_a_random_content[4].split('/')[-1]}/media/?size=l")
            elif 'tiktok.com' in get_a_random_content[4]:
                embed.set_image(url=f"https://tiktok.com/p/{get_a_random_content[4].split('/')[-1]}/media/?size=l")
            else:
                embed.set_image(url=get_a_random_content[4])
            published_datetime = get_a_random_content[6]
            published_datetime = published_datetime.strftime("%Y-%m-%d %H:%M:%S UTC")
            embed.set_footer(text=f"Content ID: {get_a_random_content[0]}")
            await interaction.edit_original_response(content=None,embed=embed, view=view)

        info_button = discord.ui.Button(
            style=discord.ButtonStyle.gray,
            label="Info",
            emoji=emojis.info,
            row=1
        )
        view.add_item(info_button)
        info_button.callback = lambda i: info_button_callback(i)

        info_button_timeout_users = {}

        async def info_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
            
            if str(interaction.user.id) in info_button_timeout_users:
                if time.time() - info_button_timeout_users[str(interaction.user.id)] < 60:
                    return await interaction.response.send_message(content=f"**You are using this button too fast. You can use this button again in <t:{int(time.time() + 60)}:R>.**",ephemeral=True)
                

            await interaction.response.defer(ephemeral=True,thinking=True)

            content_id = get_a_random_content[0]
            publisher = get_a_random_content[1]
            title = get_a_random_content[2]
            description = get_a_random_content[3] if get_a_random_content[3] != "" else "No description."
            link = get_a_random_content[4]
            guild_id = get_a_random_content[5]
            datetime = get_a_random_content[6]
            reviews = get_a_random_content[7]
            reports = get_a_random_content[8]

            try:
                guild = bot.get_guild(guild_id)
                guild_name = guild.name
            except:
                guild_name = "Not Found"

            embed = discord.Embed(
                title="Content Info",
                description="Here is the information about the content.",
                color=color.blue
            )
            embed.add_field(
                name="ID",
                value=f"```\n{content_id}```",
                inline=False
            )
            embed.add_field(
                name="Title",
                value=f"```\n{title}```",
                inline=False
            )
            embed.add_field(
                name="Description",
                value=f"```\n{description}```",
                inline=False
            )
            embed.add_field(
                name="Link",
                value=f"```\n{link}```",
                inline=False
            )
            embed.add_field(
                name="Published Guild",
                value=f"```\n{guild_name} ({guild_id})```",
                inline=False
            )
            embed.add_field(
                name="Publisher",
                value=f"<@{publisher}>",
                inline=False
            )
            embed.add_field(
                name="Published",
                value=f"<t:{int(datetime.timestamp())}>",
                inline=False
            )
            embed.add_field(
                name="Reviews",
                value=f"```\n{len(reviews)} Reviews```",
                inline=False
            )
            embed.add_field(
                name="Reports",
                value=f"```\n{len(reports)} Reports```",
                inline=False
            )
            embed.set_thumbnail(url=bot.user.display_avatar.url)
            embed.set_footer(text="Content Info")
            await interaction.edit_original_response(content=None,embed=embed, view=None)
            info_button_timeout_users[str(interaction.user.id)] = time.time()

        view.add_item(report_button)
        review_button = discord.ui.Button(
            style=discord.ButtonStyle.primary,
            label="Review",
            emoji=emojis.review,
            row=2
        )
        view.add_item(review_button)
        view.add_item(open_in_browser)
        review_button.callback = lambda i: review_button_callback(i)
        async def review_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)

            engagement_confermation_embed = discord.Embed(
                title="Engagement Confirmation",
                description="Did you engage with the content.",
                color=color.blue
            )
            engagement_confermation_embed.set_footer(text="Engagement Confirmation")
            engagement_confermation_embed.set_thumbnail(url=bot.user.display_avatar.url)

            view.clear_items()
            yes_button = discord.ui.Button(
                style=discord.ButtonStyle.success,
                label="Yes",
                emoji=emojis.yes
            )
            no_button = discord.ui.Button(
                style=discord.ButtonStyle.gray,
                label="No",
                emoji=emojis.no
            )
            view.add_item(yes_button)
            view.add_item(no_button)
            yes_button.callback = lambda i: yes_button_callback(i)
            no_button.callback = lambda i: no_button_callback(i)
            
            engaged = False
            async def yes_button_callback(interaction:discord.Interaction):
                if user.id != interaction.user.id:
                    return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                nonlocal engaged
                engaged = True

                liked = False

                liked_confermation_embed = discord.Embed(
                    title="Liked Confirmation",
                    description="Did you like the content.",
                    color=color.blue
                )
                liked_confermation_embed.set_footer(text="Liked Confirmation")
                liked_confermation_embed.set_thumbnail(url=bot.user.display_avatar.url)

                view.clear_items()
                yes_liked_button = discord.ui.Button(
                    style=discord.ButtonStyle.success,
                    label="Yes",
                    emoji=emojis.yes
                )
                no_liked_button = discord.ui.Button(
                    style=discord.ButtonStyle.gray,
                    label="No",
                    emoji=emojis.no
                )
                view.add_item(yes_liked_button)
                view.add_item(no_liked_button)
                yes_liked_button.callback = lambda i: yes_liked_button_callback(i)
                no_liked_button.callback = lambda i: no_liked_button_callback(i)



                async def last_review_callback(interaction:discord.Interaction):
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                
                    print("Content Data:",get_a_random_content[0])

                    
                    review_select = discord.ui.Select(
                        placeholder="Rate the content...",
                        options=options,
                        max_values=1,
                        min_values=1
                    )
                    nonlocal view
                    view = discord.ui.View(timeout=1000)
                    view.add_item(review_select)
                    review_select.callback = lambda i: review_select_callback(i)
                    async def review_select_callback(interaction:discord.Interaction):
                        if user.id != interaction.user.id:
                            return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)

                        rating = int(interaction.data['values'][0])

                        class review_modal(discord.ui.Modal,title="Review The Content"):
                            comment = discord.ui.TextInput(
                                label="Comment",
                                placeholder="Please write a comment about the content...",
                                required=False,
                                style=discord.TextStyle.long
                            )
                            async def on_submit(self,interaction:discord.Interaction):
                                comment = self.comment.value
                                content_id = get_a_random_content[0]
                                nonlocal liked
                                
                                screenshot = None
                                if liked:
                                    await interaction.response.edit_message(content="Please send the screenshot in the next 2 minutes.",view=None,embed=None)
                                    try:
                                        def check_same_user(message):
                                            return message.author.id == user.id and message.channel.id == interaction.channel.id
                                        screenshot = await bot.wait_for('message',timeout=120,check=check_same_user)

                                        # give the user temporary permission to upload the image and send message in the channel
                                        overwrite = discord.PermissionOverwrite()
                                        overwrite.send_messages = True
                                        overwrite.attach_files = True
                                        await interaction.channel.set_permissions(user,overwrite=overwrite)

                                        
                                        if not screenshot.attachments:
                                            await interaction.channel.set_permissions(user,overwrite=None)
                                            try:
                                                await screenshot.delete()
                                            except:
                                                None
                                            return await interaction.edit_original_response(content="You need to upload the screenshot of the content you liked.")

                                        def check_its_image(url):
                                            try:
                                                response = requests.get(url)
                                                if response.headers['content-type'].startswith('image'):
                                                    return True
                                                else:
                                                    return False
                                            except:
                                                return False
                                        if not check_its_image(screenshot.attachments[0].url):
                                            await interaction.channel.set_permissions(user,overwrite=None)
                                            try:
                                                await screenshot.delete()
                                            except:
                                                None
                                            return await interaction.edit_original_response(content="Invalid image file. Please upload a valid image file.")
                                        
                                        await interaction.channel.set_permissions(user,overwrite=None)
                                        screenshot_url = screenshot.attachments[0].url

                                        async def save_image_to_storage(url,filename):
                                            try:
                                                ss = requests.get(url)
                                                if ss.status_code == 200:
                                                    with open(f"./temp/{filename}", "wb") as f:
                                                        f.write(ss.content)
                                                    
                                                    image = Image.open(f"./temp/{filename}")
                                                    aspect_ratio = image.width / image.height

                                                    new_width = 1920
                                                    new_height = int(new_width / aspect_ratio)

                                                    if new_height > 1080:
                                                        new_height = 1080
                                                        new_width = int(new_height * aspect_ratio)

                                                    image = image.resize((new_width, new_height))

                                                    background = Image.new('RGB', (1920, 1080), 'black')
                                                    image_location = (int((1920 - image.width) / 2), int((1080 - image.height) / 2))

                                                    background.paste(image, image_location)
                                                    
                                                    background.save(f"./temp/{filename}")
                                                    
                                                    ss_channel = bot.get_channel(random.choice(ss_storage_channels))
                                                    if ss_channel:
                                                        file = discord.File(f"./temp/{filename}")
                                                        ss_message = await ss_channel.send(file=file)
                                                        return ss_message.attachments[0].url
                                                    else:
                                                        return None
                                                else:
                                                    return None
                                            except Exception as e:
                                                print(e)
                                                return None
                                        
                                        upload_to_storage = await save_image_to_storage(screenshot_url,f"{screenshot.id}.png")
                                        
                                        try:
                                            await screenshot.delete()
                                        except:
                                            pass
                                        

                                        if not upload_to_storage:
                                            return await interaction.edit_original_response(content="Failed to upload the screenshot.")
                                        screenshot = upload_to_storage
                                    except asyncio.TimeoutError:
                                        await interaction.channel.set_permissions(user,overwrite=None)
                                        try:
                                            await screenshot.delete()
                                        except:
                                            None
                                        return await interaction.edit_original_response(content="You took too long to upload the screenshot. Please try again.")
                                else:
                                    await interaction.response.edit_message(content="Submitting review...",view=None,embed=None)
                                content_data = contents.get_content_data_by_id(content_id)
                                if not content_data:
                                    return await interaction.edit_original_response(content="Content not found.")
                                
                                reviews = content_data[7]

                                user_data = users.get_user_data(user.id)
                                if not user_data:
                                    user_data = create_data.create_user_data(user.id)

                                if str(user.id) in reviews:
                                    if content_id not in user_data.get('review_ids'):
                                        review_ids=user_data.get('review_ids') if len(user_data.get('review_ids')) !=0 else []
                                        review_ids.append(content_id)
                                        user_data['review_ids'] = review_ids
                                        users.insert_user_data(user.id, user_data)
                                    return await interaction.edit_original_response(content="You have already reviewed this content.")
                                
                                reviews[str(user.id)] = {
                                    "rating": int(rating),
                                    "liked": liked,
                                    "comment": comment,
                                    "screenshot": screenshot
                                }

                                

                                contents.update_content_reviews(
                                    id=int(content_id),
                                    reviews=reviews
                                )


                                user_review_ids = user_data.get('review_ids') if len(user_data.get('review_ids')) !=0 else []
                                user_review_ids.append(content_id)
                                user_data['review_ids'] = user_review_ids
                                users.insert_user_data(user.id, user_data)
                                embed = discord.Embed(
                                    title="Review Submitted",
                                    description="Review submitted successfully.",
                                    color=color.green
                                )
                                embed.set_thumbnail(url=bot.user.display_avatar.url)
                                embed.set_footer(text="Review submitted successfully.")
                                await interaction.edit_original_response(embed=embed, view=None,content=None)

                        await interaction.response.send_modal(review_modal())
                    await interaction.response.edit_message(embed=embed,view=view)
                async def yes_liked_button_callback(interaction:discord.Interaction):
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                    nonlocal liked
                    liked = True
                    await last_review_callback(interaction)
                
                async def no_liked_button_callback(interaction:discord.Interaction):
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                    nonlocal liked
                    liked = False
                    await last_review_callback(interaction)
                    
                
                await interaction.response.edit_message(content=None,embed=liked_confermation_embed,view=view)
            
            async def no_button_callback(interaction:discord.Interaction):
                if user.id != interaction.user.id:
                    return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                nonlocal engaged
                engaged = False
                await interaction.response.edit_message(content=None,embed=embed,view=None)


            await interaction.response.edit_message(content=None,embed=engagement_confermation_embed,view=view)



        content_message=await interaction.edit_original_response(embed=embed,view=view)

    account_button = discord.ui.Button(
        style=discord.ButtonStyle.primary,
        label="Account",
        emoji=emojis.account
    )
    account_button.callback = lambda i: account_button_callback(i)
    

    notification_button = discord.ui.Button(
        style=discord.ButtonStyle.primary,
        label="Notification",
        emoji=emojis.notification
    )
    notification_button.callback = lambda i: notification_button_callback(i)

    async def notification_button_callback(interaction:discord.Interaction):
        await interaction.response.defer(ephemeral=True,thinking=True)
        if check_ban(interaction.user.id):
            return await interaction.edit_original_response(content=f"You are banned from using this bot.",)
        
        user_data = users.get_user_data(interaction.user.id)

        if not user_data:
            return await interaction.edit_original_response(content="You have not posted any content yet.")
        
        user_post_ids = user_data.get('post_ids') if len(user_data.get('post_ids')) !=0 else []

        if len(user_post_ids) == 0:
            return await interaction.edit_original_response(content="You have not posted any content yet.")
        
        all_reviews = contents.get_all_reviews_by_publisher(interaction.user.id,1)
        if not all_reviews or len(all_reviews) == 0:
            return await interaction.edit_original_response(content="You have not received any reviews yet.")
        
        current_page_index = 0
        current_content_id = None

        view = discord.ui.View(timeout=None)
        
        timeout_time = 120
        cancled = False
        def reset_timeout():
            nonlocal timeout_time
            timeout_time = 120

        user = interaction.user

        def get_page(index):
            page = all_reviews[index]
            return page
        
        embed = discord.Embed(
            title="Your Reviews",
            description="Here is the information about your reviews.",
            color=color.green
        )
        def get_embed(page_index,embed):
            page = get_page(page_index)
            embed.clear_fields()
            for review in page:
                nonlocal current_content_id
                id = review.get('id')
                current_content_id = id
                publisher = review.get('publisher')
                title = review.get('title')
                description = review.get('description')
                link = review.get('link')
                guild_id = review.get('guild_id')
                created_at = review.get('created_at')
                user = review.get('user')
                

                rating = review.get('review')

                embed.add_field(
                    name=f"**{title}**",
                    value = f"**• ID:** {id}\n**• Publisher:** <@{publisher}>\n**• Published:** <t:{int(created_at.timestamp())}>\n**• Guild:** `{guild_id}`\n**• Link:** {link}\n**• User:** <@{user}>\n**• Rating:** `{rating.get('rating')}/5`\n**• Liked:** `{rating.get('liked')}`\n\n**• Comment:** \n```\n{rating.get('comment')}```",
                    inline=False
                )
                get_review_screenshot = rating.get('screenshot')
                if get_review_screenshot and ('http' in get_review_screenshot or 'https' in get_review_screenshot):
                    embed.set_image(url=get_review_screenshot)
                else:
                    embed.set_image(url=None)


            return embed

        def get_view(view,disabled=False):
            view.clear_items()
            previous_button = discord.ui.Button(
                style=discord.ButtonStyle.primary,
                label="Previous",
                emoji=emojis.back,
                row=1,
                disabled=True if current_page_index <= 0 else False
            )
            stop_button = discord.ui.Button(
                style=discord.ButtonStyle.danger,
                label="Stop",
                emoji=emojis.stop,
                row=1
            )
            next_button = discord.ui.Button(
                style=discord.ButtonStyle.primary,
                label="Next",
                emoji=emojis.arrow,
                row=1,
                disabled=True if current_page_index >= len(all_reviews)-1 else False
            )
            report_reviewer_button = discord.ui.Button(
                style=discord.ButtonStyle.primary,
                label="Report Reviewer",
                emoji=emojis.report,
                row=2
            )
            report_reviewer_button.callback = lambda i: report_reviewer_button_callback(i)
            previous_button.callback = lambda i: previous_button_callback(i)
            stop_button.callback = lambda i: stop_button_callback(i)
            next_button.callback = lambda i: next_button_callback(i)
            if disabled:
                previous_button.disabled = True
                stop_button.disabled = True
                next_button.disabled = True
            view.add_item(previous_button)
            view.add_item(stop_button)
            view.add_item(next_button)
            view.add_item(report_reviewer_button)
            return view

        async def report_reviewer_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)

            class report_reviewer(discord.ui.Modal,title="Report Reviewer"):
                reason = discord.ui.TextInput(
                    label="Reason",
                    placeholder="Please write a reason for reporting the reviewer...",
                    required=True,
                    style=discord.TextStyle.long
                )
                async def on_submit(self,interaction:discord.Interaction):
                    reason = self.reason.value
                    user_data = users.get_user_data(user.id)
                    if not user_data:
                        user_data = create_data.create_user_data(user.id)
                    reviews = user_data.get('review_ids')
                    if current_content_id in reviews:
                        return await interaction.response.edit_message(content="You have already reported this reviewer.")
                    
                    
                    async def send_report_log_report_reviewer(guild_id,author,content_id,reason):
                        nonlocal current_page_index
                        page = get_page(current_page_index)
                        for review in page:
                            id = review.get('id')
                            current_content_id = id
                            publisher = review.get('publisher')
                            title = review.get('title')
                            description = review.get('description')
                            link = review.get('link')
                            
                            created_at = review.get('created_at')
                            user = review.get('user')
                            
                            review = review.get('review')

                        guild_data = guilds.get_guild_data(guild_id)
                        if not guild_data:
                            return
                        report_channel = guild_data.get('report_channel')
                        if not report_channel:
                            return
                        report_channel = bot.get_channel(report_channel)
                        if not report_channel:
                            return
                        
                        embed = discord.Embed(
                            title="Reviewer Reported by Publisher",
                            description=f"<@{user}> is reported by <@{author}>",
                            color=color.orange
                        )
                        embed.add_field(
                            name="Content ID",
                            value=f"```\n{content_id}```",
                            inline=False
                        )
                        embed.add_field(
                            name="Reason",
                            value=f"```\n{reason}```",
                            inline=False
                        )
                        embed.add_field(
                            name="Content Publisher",
                            value=f"<@{publisher}>",
                            inline=False
                        )
                        embed.add_field(
                            name="Published",
                            value=f"<t:{int(created_at.timestamp())}>",
                            inline=False
                        )
                        embed.add_field(
                            name="Link",
                            value=f"```\n{link}```",
                            inline=False
                        )

                        embed.add_field(
                            name="Comment",
                            value=f"```\n{review.get('comment')}```",
                            inline=False
                        )
                        if review.get('screenshot'):
                            embed.set_image(url=review.get('screenshot'))
                        else:
                            embed.set_image(url=None)

                        embed.set_thumbnail(url=bot.user.display_avatar.url)
                        embed.set_footer(text="Reviewer Reported by Publisher")
                        await report_channel.send(embed=embed)
                    
                    
                    await send_report_log_report_reviewer(interaction.guild.id,interaction.user.id,current_content_id,reason)
                    await interaction.response.edit_message(embed=embed, view=None,content=None)
            await interaction.response.send_modal(report_reviewer())



        async def previous_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
            nonlocal current_page_index,embed,view
            reset_timeout()
            current_page_index -= 1
            embed = get_embed(current_page_index,embed)
            view = get_view(view)
            await interaction.response.edit_message(embed=embed,view=view)

        async def stop_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
            reset_timeout()
            nonlocal view,cancled
            view = get_view(view,disabled=True)
            await interaction.response.edit_message(view=view)
            cancled = True
        
        async def next_button_callback(interaction:discord.Interaction):
            if user.id != interaction.user.id:
                return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
            nonlocal current_page_index,embed,view
            reset_timeout()
            current_page_index += 1
            embed = get_embed(current_page_index,embed)
            view = get_view(view)
            await interaction.response.edit_message(embed=embed,view=view)
        


        embed = get_embed(current_page_index,embed)
        view = get_view(view)
        message = await interaction.edit_original_response(embed=embed,view=view)

        while True:
            if cancled:
                break
            if timeout_time <= 0:
                view = get_view(view,disabled=True)
                try:
                    await message.edit(view=view)
                except:
                    None
                break
            await asyncio.sleep(1)
            timeout_time -= 1
            




    website_button = discord.ui.Button(
        style=discord.ButtonStyle.link,
        label="Content Match Website",
        url="https://www.contentmatch.io",
        emoji=emojis.website,
        row=2
    )
    

    async def account_button_callback(interaction:discord.Interaction):
        await interaction.response.defer(ephemeral=True,thinking=True)

        if check_ban(interaction.user.id):
            return await interaction.edit_original_response(content=f"You are banned from using this bot.")
        
        user_data = users.get_user_data(interaction.user.id)
        if not user_data:
            user_data = create_data.create_user_data(interaction.user.id)

        user_post_ids = user_data.get('post_ids') if len(user_data.get('post_ids')) !=0 else []
        user_review_ids = user_data.get('review_ids') if len(user_data.get('review_ids')) !=0 else []
        user_report_ids = user_data.get('report_ids') if len(user_data.get('report_ids')) !=0 else []

        def get_len_of_reviews_and_reports(publisher):
            content_data = contents.get_content_data_by_publisher(publisher)
            if not content_data:
                return 0,0,0

            reviews = 0
            reports = 0
            liked = 0
            for content in content_data:
                for i,data in (content[7]).items():
                    if data.get('liked'):
                        liked += 1
                reviews += len(content[7])
                reports += len(content[8])
            return reviews,reports,liked
        user = interaction.user

        def get_account_home_embed():
            embed = discord.Embed(
                title="Your Account Info",
                description="Here is the information about your account.",
                color=color.blue
            )
            embed.add_field(
                name="General Info",
                value=f"**• User ID:** `{interaction.user.id}`\n**• Username:** `{interaction.user.name}`\n**• Account Created:** <t:{int(interaction.user.created_at.timestamp())}:F>",
                inline=True
            )
            embed.add_field(
                name="",
                value="",
                inline=False,
            )
            reviews,reports,liked = get_len_of_reviews_and_reports(interaction.user.id)
            embed.add_field(
                name="Your All Posts",
                value=f"**• Posts:** `{len(user_post_ids)}`\n**• Reviews:** `{reviews}`\n**• Reports:**` {reports}`\n**• Liked:** `{liked}`",
                inline=True
            )
            embed.add_field(
                name="Given Reviews & Reports",
                value=f"**• Reviews:** `{len(user_review_ids)}`\n**• Reports:** `{len(user_report_ids)}`",
                inline=True
            )
            embed.set_thumbnail(url=user.display_avatar.url)
            embed.set_footer(text="Your Account Info")
            return embed


        
        def get_account_home_view(view:discord.ui.View):
            view.clear_items()
            contents_button = discord.ui.Button(
                style=discord.ButtonStyle.primary,
                label="Contents",
                emoji=emojis.content
            )
            contents_button.callback = lambda i: contents_button_callback(i)
            view.add_item(contents_button)

            async def contents_button_callback(interaction:discord.Interaction):
                await interaction.response.edit_message(content="Loading...",embed=None,view=None)
                if user.id != interaction.user.id:
                    await interaction.edit_original_response(content=f"Only {user.mention} can use this button.")
                    return
                user_data = users.get_user_data(user.id)
                if not user_data:
                    user_data = create_data.create_user_data(user.id)
                user_post_ids = user_data.get('post_ids') if len(user_data.get('post_ids')) !=0 else []

                if len(user_post_ids) == 0:
                    return await interaction.edit_original_response(content="You have not posted any content yet.")

                content_data = contents.get_all_content_by_page(
                    limit_per_page=1,
                    publisher=user.id
                )
                if not content_data:
                    return await interaction.edit_original_response(content="You have not posted any content yet.")
                current_page_index = 0
                current_content_id = None

                view = discord.ui.View(timeout=None)
                timeout_time = 120
                cancled = False
                def reset_timeout():
                    nonlocal timeout_time
                    timeout_time = 120

                def get_page(index):
                    content_data = contents.get_all_content_by_page(
                        limit_per_page=1,
                        publisher=user.id
                    )
                    page = content_data[index]
                    return page
                async def get_embed(page_index,embed):
                    nonlocal current_content_id
                    page = get_page(page_index)
                    embed.clear_fields()
                    for content in page:
                        current_content_id = content[0]
                        content_id = content[0]
                        publisher = content[1]
                        title = content[2]
                        description = content[3]
                        link = content[4]
                        guild_id = content[5]
                        datetime = content[6]
                        reviews = content[7]
                        reports = content[8]

                        try:
                            guild = await bot.get_guild(guild_id)
                            guild_name = guild.name
                        except:
                            guild_name = "Not Found"
                        
                        embed.add_field(
                            name=f"**{title}**",
                            value=f"**• ID:** {content_id}\n**• Publisher:** <@{publisher}>\n**• Published:** <t:{int(datetime.timestamp())}>\n**• Guild:** {guild_name}\n**• Link:** {link}\n**• Reviews:** {len(reviews)}\n**• Reports:** {len(reports)}",
                            inline=False
                        )
                        embed.set_footer(text=f"Page: {current_page_index + 1}/{len(content_data)}")
                    return embed
                
                def get_view(view:discord.ui.View,disabled=False):
                    view.clear_items()
                    previous_button = discord.ui.Button(
                        style=discord.ButtonStyle.primary,
                        emoji=emojis.back,
                        row=1,
                        disabled=True if current_page_index <= 0 else False
                    )

                    stop_button = discord.ui.Button(
                        style=discord.ButtonStyle.danger,
                        emoji=emojis.stop,
                        row=1
                    )
                    next_button = discord.ui.Button(
                        style=discord.ButtonStyle.primary,
                        emoji=emojis.arrow,
                        row=1,
                        disabled=True if current_page_index >= len(content_data) - 1 else False
                    )
                    edit_button = discord.ui.Button(
                        style=discord.ButtonStyle.primary,
                        label="Edit",
                        emoji=emojis.edit,
                        row=2
                    )
                    back_button = discord.ui.Button(
                        style=discord.ButtonStyle.primary,
                        label="Back",
                        emoji=emojis.back,
                        row=2
                    )

                    delete_button = discord.ui.Button(
                        style=discord.ButtonStyle.danger,
                        label="Delete",
                        emoji=emojis.delete,
                        row=2
                    )

                    view.add_item(previous_button)
                    view.add_item(stop_button)
                    view.add_item(next_button)
                    view.add_item(edit_button)
                    view.add_item(back_button)
                    view.add_item(delete_button)

                    if disabled:
                        previous_button.disabled = True
                        next_button.disabled = True
                        stop_button.disabled = True
                        edit_button.disabled = True
                        back_button.disabled = True

                    previous_button.callback = lambda i: previous_button_callback(i)
                    next_button.callback = lambda i: next_button_callback(i)
                    stop_button.callback = lambda i: stop_button_callback(i)
                    edit_button.callback = lambda i: edit_button_callback(i)
                    back_button.callback = lambda i: back_button_callback(i)
                    delete_button.callback = lambda i: delete_button_callback(i)
                    return view
                
                async def delete_button_callback(interaction:discord.Interaction):
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                    content = contents.get_content_data_by_id(current_content_id)
                    if not content:
                        return await interaction.response.edit_message(content="Content not found.",view=None,embed=None)
                    publisher = content[1]
                    if publisher != user.id:
                        return await interaction.response.edit_message(content="You can't delete others content.",view=None,embed=None)
                    contents.delete_content_data(current_content_id)
                    user_data = users.get_user_data(user.id)
                    user_post_ids = user_data.get('post_ids') if len(user_data.get('post_ids')) !=0 else []
                    user_post_ids.remove(current_content_id)
                    user_data['post_ids'] = user_post_ids
                    users.insert_user_data(user.id,user_data)
                    await interaction.response.edit_message(content="✅Content deleted successfully.",view=None,embed=None)


                async def back_button_callback(interaction:discord.Interaction):
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                    reset_timeout()
                    
                    nonlocal view
                    embed = get_account_home_embed()
                    view = get_account_home_view(view)
                    await interaction.response.edit_message(content=None,embed=embed, view=view)

                async def previous_button_callback(interaction:discord.Interaction):
                    await interaction.response.edit_message(content="Loading...",embed=None,view=None)
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                    nonlocal current_page_index
                    current_page_index -= 1
                    reset_timeout()
                    nonlocal embed,view
                    embed = await get_embed(current_page_index,embed)
                    view = get_view(view)
                    await interaction.edit_original_response(content=None,embed=embed, view=view)
                async def next_button_callback(interaction:discord.Interaction):
                    await interaction.response.edit_message(content="Loading...",embed=None,view=None)
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                    nonlocal current_page_index
                    current_page_index += 1
                    reset_timeout()
                    nonlocal embed,view
                    embed = await get_embed(current_page_index,embed)
                    view = get_view(view)
                    await interaction.edit_original_response(content=None,embed=embed, view=view)
                async def stop_button_callback(interaction:discord.Interaction):
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)
                    nonlocal view
                    view = get_view(view,disabled=True)
                    await interaction.response.edit_message(view=view)
                    nonlocal cancled
                    cancled = True

                async def edit_button_callback(interaction:discord.Interaction):
                    if user.id != interaction.user.id:
                        return await interaction.response.send_message(content=f"Only {user.mention} can use this button.",ephemeral=True)

                    content = contents.get_content_data_by_id(current_content_id)
                    if not content:
                        return await interaction.edit_original_response(content="Content not found.")
                    publisher = content[1]
                    if publisher != user.id:
                        return await interaction.edit_original_response(content="You can't edit others content.")
                    
                    reset_timeout()



                    class edit_content_modal(discord.ui.Modal,title="Edit Content"):
                        post_title = discord.ui.TextInput(
                            label="Post Title",
                            placeholder="Title...",
                            required=True,
                            style=discord.TextStyle.short,
                            row=1,
                            default=content[2]
                        )
                        description = discord.ui.TextInput(
                            label="Post Description",
                            placeholder="Description...",
                            style=discord.TextStyle.long,
                            required=False,
                            row=2,
                            default=content[3]
                        )
                        link = discord.ui.TextInput(
                            label="Link",
                            placeholder="Link...",
                            required=True,
                            style=discord.TextStyle.short,
                            row=3,
                            default=content[4]
                        )
                        async def on_submit(self,interaction:discord.Interaction):
                            title = self.post_title.value
                            description = self.description.value
                            link = self.link.value
                            if not is_valid_content_link(link):
                                return await interaction.response.send_message(content="Invalid link.",ephemeral=True)
                            if contents.check_content_by_link(link,content[0]):
                                return await interaction.response.send_message(content=f"{emojis.error} This content is already posted.",ephemeral=True)

                            
                            contents.update_content_data(
                                id=content[0],
                                title=title,
                                description=description,
                                link=link
                            )
                            nonlocal view,embed
                            embed = await get_embed(current_page_index,embed)
                            view = get_view(view)
                            await interaction.response.edit_message(content=None,embed=embed, view=view)

                    await interaction.response.send_modal(edit_content_modal())

                nonlocal  embed
                embed = await get_embed(current_page_index,embed)
                view = get_view(view)
                
                await interaction.edit_original_response(content=None,embed=embed, view=view)
            
            return view

        embed = get_account_home_embed()
        view = discord.ui.View(timeout=None)
        view = get_account_home_view(view)

        await interaction.edit_original_response(content=None,embed=embed, view=view)


    view.add_item(play_content_button)
    view.add_item(post_button)
    view.add_item(account_button)
    view.add_item(notification_button)
    view.add_item(website_button)
    
    if forced:
        # try delete old message
        try:
            main_menu_message = guild_data.get('main_menu_message')
            main_menu_message = await main_menu_channel.fetch_message(main_menu_message)
            await main_menu_message.delete()
        except Exception as e:
            print(e)

    def check(message):
        return message.id != main_menu_message.id
    try:
        main_menu_message = guild_data.get('main_menu_message')
        main_menu_message = await main_menu_channel.fetch_message(main_menu_message)

        await main_menu_message.edit(embed=embed,view=view)
           
    except:
        message = await main_menu_channel.send(embed=embed, view=view)
        guild_data['main_menu_message'] = message.id
        guilds.insert_guild_data(guild_id, guild_data)
    
    def check(message):
        return message.id != guild_data['main_menu_message']
    try:
        await main_menu_channel.purge(limit=1000,check=check)
    except discord.Forbidden:
        pass
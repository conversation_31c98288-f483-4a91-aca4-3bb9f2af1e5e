# Import necessary modules and functions
from data import emojis
from functions.checks import check_perms, check_bot_owner
from functions.startups import start_main_menu

# Define an asynchronous function to reload the bot
async def reload(ctx, bot):
    # Check if the user has the necessary permissions to use the command
    if not check_perms(ctx) and not check_bot_owner(ctx.author.id) and ctx.author.id != ctx.guild.owner.id:
        # If not, send an error message and return
        return await ctx.send(f"**{emojis.error} You do not have permission to use this command.**")
    
    # Send a loading message
    message = await ctx.send(f"{emojis.loading} Reloading...")
    try:
        # Try to start the main menu of the bot
        await start_main_menu(bot, guild_id=ctx.guild.id)
        # If successful, edit the message to show that the reload was successful
        await message.edit(content=f"{emojis.success} Reloaded successfully.")
    except Exception as e:
        # If an error occurs, edit the message to show that the reload failed
        await message.edit(content=f"{emojis.error} Failed to reload the database.")
        # Print the error
        print(e)
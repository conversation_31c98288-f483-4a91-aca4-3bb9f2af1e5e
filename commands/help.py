import discord

from src.config import config

async def help(ctx,bot):
    description = []
    for command in bot.commands:
        if command.hidden:
            continue
        description.append(f'**🔍 - {config.prefix}{command.name}** - {command.help}')
    embed = discord.Embed(
        title='📚All help Commands',
        description='\n'.join(description),
        color=0x00ff00
    )
    embed.set_footer(text=f'Requested by {ctx.author.name}#{ctx.author.discriminator}')
    embed.set_thumbnail(url=bot.user.display_avatar.url)
    await ctx.send(embed=embed)
# Import necessary modules
import discord

# Import the configuration and the bans database
from src.config import config
from database import bans

# Define an asynchronous function to ban a user
async def banuser(ctx, user:discord.User, reason:str):
    # Check if the author of the command is an owner
    if ctx.author.id not in config.owner_ids:
        return
    # If no reason is provided, set a default reason
    if not reason:
        reason = "No reason provided."
    # If no user is provided, send an error message and return
    if not user:
        return await ctx.send(f"Invalid Syntax. Use `{ctx.prefix}banuser <user> <reason>`")
    # If the user is already banned, send a message and return
    if bans.get_ban_data_by_user_id(user.id):
        return await ctx.send(f"{user.mention} is already banned.")
    # Ban the user
    bans.ban_user(user.id, reason)
    # Send a message indicating that the user has been banned
    await ctx.send(f"{user.mention} has been banned from using the bot. Reason: {reason}", delete_after=10)
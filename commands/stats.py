import sys
import discord
import psutil
import time
import platform


from src.config import config
from src import color

bot_start_time = time.time()

async def stats(ctx,bot):
    cpu_uses_percent = psutil.cpu_percent()
    memory = psutil.virtual_memory()
    memory_used = memory.used
    memory_total = memory.total
    memory_percent = memory.percent

    python_version = sys.version.split(" ")[0]
    discord_version = discord.__version__
    bot_version = config.version

    embed = discord.Embed(
        title="Bot Stats",
        color=color.random_color()
    )

    cpu_percentage_bar = "█" * int(cpu_uses_percent/2)
    cpu_percentage_bar += " " * (50 - int(cpu_uses_percent/2))

    ram_percentage_bar = "█" * int(memory_percent/2)
    ram_percentage_bar += " " * (50 - int(memory_percent/2))

    embed.add_field(name=f"CPU Usage",value=f"`{cpu_percentage_bar}`{cpu_uses_percent}%",inline=False)
    embed.add_field(name=f"Memory Usage ({memory_percent}%)",value=f"`{ram_percentage_bar}`{memory_used/1_000_000_000:.2f}/{memory_total/1_000_000_000:.2f} GB",inline=False)
    embed.add_field(name="",value="",inline=False)
    embed.add_field(name="Python Version",value=f"```fix\n{python_version}```",inline=True)
    embed.add_field(name="Discord.py Version",value=f"```fix\n{discord_version}```",inline=True)
    embed.add_field(name="Bot Version",value=f"```fix\n{bot_version}```",inline=True)
    embed.add_field(name="System",value=f"```fix\n{platform.system()} {platform.release()}```",inline=True)

    uptime_seconds = int(time.time() - bot_start_time)
    uptime_days = uptime_seconds // (24 * 3600)
    uptime_seconds %= (24 * 3600)
    uptime_hours = uptime_seconds // 3600
    uptime_seconds %= 3600
    uptime_minutes = uptime_seconds // 60

    uptime_formatted = f"{uptime_days}d {uptime_hours}h {uptime_minutes}m"
    embed.add_field(name="Uptime",value=f"```fix\n{uptime_formatted}```",inline=True)

    embed.set_footer(text=f"Requested by {ctx.author}",icon_url=ctx.author.display_avatar.url)
    embed.set_thumbnail(url=bot.user.display_avatar.url)
    await ctx.reply(embed=embed)
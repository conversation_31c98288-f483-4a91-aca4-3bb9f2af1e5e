import discord
from discord.ext import commands
import asyncio
from src.config import config
from data import emojis
from src import color
from database import contents
from functions.checks import check_perms,check_bot_owner


async def deletecontent(ctx:commands.Context,content_id:int):

    bot_owner = check_bot_owner(ctx.author.id)

    message = await ctx.send(f"{emojis.loading} Loading...")

    if not content_id:
        return await message.edit(content=f"**{emojis.error} Invalid Syntax. Use `{config.prefix}deletecontent <content_id>`**")
    content_data = contents.get_content_data_by_id(content_id)
    if not content_data:
        return await message.edit(content=f"**{emojis.error} No content found with that ID.**")
    
    content_owner = content_data[1] == ctx.author.id
    content_guild_id = content_data[5]

    if not check_perms(ctx) and not bot_owner and not content_owner:
        return await message.edit(content=f"**{emojis.error} You do not have permission to use this command.**")
    if content_guild_id != ctx.guild.id and not bot_owner and not content_owner:
        return await message.edit(content=f"**{emojis.error} This content is not uploaded from this server.**")
    

    
    confermation_embed = discord.Embed(
        title="Are you sure?",
        description=f"Are you sure you want to delete this content?",
        color=color.red
    )
    view = discord.ui.View(timeout=500)
    timeout_time = 120
    def reset_timeout():
        nonlocal timeout_time
        timeout_time = 120
    cancled = False
    yes_button = discord.ui.Button(
        style=discord.ButtonStyle.success,
        label="Yes",
        emoji=emojis.yes
    )
    yes_button.callback = lambda i: yes_button_callback(i)

    async def yes_button_callback(interaction:discord.Interaction):
        await interaction.response.edit_message(content=f"{emojis.loading} Deleting...",view=None,embed=None)
        if ctx.author != interaction.user:
            return await interaction.edit_original_response(content=f"Only {ctx.author.mention} can use this button.")
        reset_timeout()
        contents.delete_content_data(content_id)
        embed = discord.Embed(
            title="Deleted",
            description="The content has been deleted permanently.",
            color=color.green
        )
        await interaction.edit_original_response(content=None,embed=embed, view=None)
        nonlocal cancled
        cancled = True

    no_button = discord.ui.Button(
        style=discord.ButtonStyle.gray,
        label="No",
        emoji=emojis.no
    )
    no_button.callback = lambda i: no_button_callback(i)

    async def no_button_callback(interaction:discord.Interaction):
        await interaction.response.edit_message(content=f"{emojis.loading} Canceling...",view=None,embed=None)
        if ctx.author != interaction.user:
            return await interaction.edit_original_response(content=f"Only {ctx.author.mention} can use this button.")
        reset_timeout()
        embed = discord.Embed(
            title="Canceled",
            description="The deletion has been canceled.",
            color=color.red
        )
        await interaction.edit_original_response(content=None,embed=embed, view=None)
        nonlocal cancled
        cancled = True

    view.add_item(yes_button)
    view.add_item(no_button)
    await message.edit(content=None,embed=confermation_embed, view=view)

    while True:
        timeout_time -= 1
        if timeout_time <= 0:
            timeout_embed = discord.Embed(
                title="Timeout",
                description="You took too long to respond.",
                color=color.red
            )
            await message.edit(content=None, embed=timeout_embed, view=None)
            break
        if cancled:
            break
        await asyncio.sleep(1)
import discord
from discord.ext import commands
import asyncio
from src.config import config
from data import emojis
from src import color
from database import guilds
from functions import create_data
from functions.checks import check_perms,check_bot_owner
from functions.startups import start_main_menu



async def setup(ctx,bot):
    if not check_perms(ctx) and not check_bot_owner(ctx.author.id) and not ctx.author.id in config.admin_ids:
        return await ctx.send(f"**{emojis.error} You do not have permission to use this command.**")
    
    guild_data = guilds.get_guild_data(ctx.guild.id)
    if not guild_data:
        guild_data = create_data.create_guild_data(ctx.guild.id)
        
    embed = discord.Embed(
        title="Set The Main Menu Channel",
        description="Please select the channel where you want the main menu to be.",
        color=color.green
    )
    
    async def get_existing_data(guild_data):
        try:
            main_menu_channel = guild_data.get('main_menu_channel')
            main_menu_channel = await bot.fetch_channel(main_menu_channel)
            main_menu_channel = main_menu_channel.mention
        except:
            main_menu_channel = "Not Found"
        
        try:
            report_channel = guild_data.get('report_channel')
            report_channel = await bot.fetch_channel(report_channel)
            report_channel = report_channel.mention
        except:
            report_channel = "Not Found"
        
        try:
            moderator_role = guild_data.get('moderator_role')
            moderator_role = ctx.guild.get_role(moderator_role)
            moderator_role = moderator_role.mention
        except:
            moderator_role = "Not Found"
        
        return main_menu_channel, report_channel, moderator_role
    
    existing_data = await get_existing_data(guild_data)
    embed.add_field(
        name="Existing configuration",
        value=f"**• Main Menu Channel:** {existing_data[0]}\n**• Report Channel:** {existing_data[1]}\n**• Moderator Role:** {existing_data[2]}"
    )
    embed.set_footer(text="This is the channel where the main menu will be displayed.")
    embed.set_thumbnail(url=bot.user.display_avatar.url)

    view = discord.ui.View(timeout=500)

    timeout_time = 120
    cancled = False

    def reset_timeout():
        nonlocal timeout_time
        timeout_time = 120
    
    guild_data = guilds.get_guild_data(ctx.guild.id)
    old_sellect_main_channel = guild_data.get('main_menu_channel')
    old_sellect_main_message = guild_data.get('main_menu_message')

    sellect_main_channel = discord.ui.ChannelSelect(
        channel_types=[discord.ChannelType.text],
        placeholder="Select a channel...",
        min_values=1,
        max_values=1
    )
    sellect_main_channel.callback = lambda i: sellect_main_channel_callback(i)

    async def sellect_main_channel_callback(interaction:discord.Interaction):
        if ctx.author != interaction.user:
            return await interaction.response.send_message(f"Only {ctx.author.mention} can use this button.", ephemeral=True)
        if not interaction.data.get('values'):
            return await interaction.response.send_message("You need to select a channel.", ephemeral=True)
        reset_timeout()
        channel_id = interaction.data.get('values')[0]

        try:
            channel = await bot.fetch_channel(channel_id)
        except:
            return await interaction.response.send_message("I could not find that channel.", ephemeral=True)

        guild_data['main_menu_channel'] = channel.id
        guilds.insert_guild_data(ctx.guild.id, guild_data)

        embed = discord.Embed(
            title="Report Channel Set",
            description=f"Set the channel where all the reports will be sent.",
            color=color.green
        )
        existing_data = await get_existing_data(guild_data)
        embed.add_field(
            name="Existing configuration",
            value=f"**• Main Menu Channel:** {existing_data[0]}\n**• Report Channel:** {existing_data[1]}\n**• Moderator Role:** {existing_data[2]}"
        )
        embed.set_footer(text="This is the channel where the main menu will be displayed.")
        embed.set_thumbnail(url=bot.user.display_avatar.url)
        view.clear_items()

        select_report_channel = discord.ui.ChannelSelect(
            channel_types=[discord.ChannelType.text],
            placeholder="Select a report channel..",
            min_values=1,
            max_values=1,
        )
        select_report_channel.callback = lambda i: select_report_channel_callback(i)
        view.add_item(select_report_channel)

        async def select_report_channel_callback(interaction:discord.Interaction):
            if ctx.author != interaction.user:
                return await interaction.response.send_message(f"Only {ctx.author.mention} can use this button.", ephemeral=True)
            if not interaction.data.get('values'):
                return await interaction.response.send_message("You need to select a channel.", ephemeral=True)
            reset_timeout()
            channel_id = interaction.data.get('values')[0]

            try:
                channel = await bot.fetch_channel(channel_id)
            except:
                return await interaction.response.send_message("I could not find that channel.", ephemeral=True)

            guild_data['report_channel'] = channel.id
            guilds.insert_guild_data(ctx.guild.id, guild_data)

            embed = discord.Embed(
                title="Moderator Role Set",
                description="Please mention the role that will be used as the moderator role.",
                color=color.green
            )
            existing_data = await get_existing_data(guild_data)
            embed.add_field(
                name="Existing configuration",
                value=f"**• Main Menu Channel:** {existing_data[0]}\n**• Report Channel:** {existing_data[1]}\n**• Moderator Role:** {existing_data[2]}"
            )
            embed.set_footer(text="This is the role that will be used as the moderator role.")
            embed.set_thumbnail(url=bot.user.display_avatar.url)
            view.clear_items()

            select_moderator_role = discord.ui.RoleSelect(
                placeholder="Select Moderator role...",
                min_values=1,
                max_values=1
            )
            select_moderator_role.callback = lambda i: select_moderator_role_callback(i)
            view.add_item(select_moderator_role)

            async def select_moderator_role_callback(interaction:discord.Interaction):
                if ctx.author != interaction.user:
                    return await interaction.response.send_message(f"Only {ctx.author.mention} can use this button.", ephemeral=True)
                if not interaction.data.get('values'):
                    return await interaction.response.send_message("You need to select a role.", ephemeral=True)
                reset_timeout()
                role_id = interaction.data.get('values')[0]

                try:
                    role = ctx.guild.get_role(int(role_id))
                    if not role:
                        return await interaction.response.send_message("I could not find that role.", ephemeral=True)
                except:
                    return await interaction.response.send_message("I could not find that role.", ephemeral=True)
                
                guild_data['moderator_role'] = role.id
                guilds.insert_guild_data(ctx.guild.id, guild_data)

                embed = discord.Embed(
                    title="Setup Complete",
                    description="The setup is now complete.",
                    color=color.green
                )
                existing_data = await get_existing_data(guild_data)
                embed.add_field(
                    name="Existing configuration",
                    value=f"**• Main Menu Channel:** {existing_data[0]}\n**• Report Channel:** {existing_data[1]}\n**• Moderator Role:** {existing_data[2]}"
                )
                embed.set_footer(text="Setup is now complete.")
                embed.set_thumbnail(url=bot.user.display_avatar.url)
                view.clear_items()

                await interaction.response.edit_message(embed=embed, view=None)
                nonlocal cancled
                cancled = True
                try:
                    nonlocal old_sellect_main_channel, old_sellect_main_message
                    old_sellect_main_channel  = await interaction.guild.fetch_channel(int(old_sellect_main_channel))
                    old_sellect_main_message = await old_sellect_main_channel.fetch_message(int(old_sellect_main_message))
                    await old_sellect_main_message.delete()
                except Exception as e:
                    print(e)
                
                await start_main_menu(bot,ctx.guild.id,forced=True)
            
            await interaction.response.edit_message(embed=embed, view=view)

        await interaction.response.edit_message(embed=embed, view=None)
        await interaction.edit_original_response(embed=embed, view=view)

    view.add_item(sellect_main_channel)

    message = await ctx.send(embed=embed, view=view)

    while True:
        timeout_time -= 1
        if timeout_time <= 0:
            timeout_embed = discord.Embed(
                title="Timeout",
                description="You took too long to respond.",
                color=color.red
            )
            await message.edit(content=None, embed=timeout_embed, view=None)
            break
        if cancled:
            break
        await asyncio.sleep(1)
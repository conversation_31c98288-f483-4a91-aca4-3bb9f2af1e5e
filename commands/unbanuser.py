import discord

from src.config import config
from database import bans

async def unbanuser(ctx,user:discord.User):
    if ctx.author.id not in config.owner_ids:
        return
    if not user:
        return await ctx.send(f"Invalid Syntax. Use `{ctx.prefix}unbanuser <user>`")
    if not bans.get_ban_data_by_user_id(user.id):
        return await ctx.send(f"{user.mention} is not banned.")
    bans.unban_user(user.id)
    await ctx.send(f"{user.mention} has been unbanned from using the bot.",delete_after=10)

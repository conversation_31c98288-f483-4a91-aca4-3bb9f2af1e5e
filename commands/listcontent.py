import discord
import asyncio

from data import emojis
from src import color

from database import contents
from functions.checks import check_perms,check_bot_owner


async def listcontent(ctx,bot):
    bot_owner = check_bot_owner(ctx.author.id)
    checked_admin = check_perms(ctx)
    if not checked_admin and not bot_owner and ctx.author.id != ctx.guild.owner.id:
        return await ctx.send(f"**{emojis.error} You do not have permission to use this command.**")
    
    message = await ctx.reply(f"{emojis.loading} Loading...")

    content_data = contents.get_all_content_by_page(limit_per_page=3,guild_id=ctx.guild.id if not bot_owner else None)
    
    if not content_data:
        return await ctx.send(f"**{emojis.error} No content found.**")
    
    embed = discord.Embed(
        title="Content List",
        description="Here are the list of contents.",
        color=color.blue
    )
    embed.set_thumbnail(url=bot.user.display_avatar.url)
    

    view = discord.ui.View(timeout=500)
    timeout_time = 120
    cancled = False
    
    current_page_index = 0
    
    def reset_timeout():
        nonlocal timeout_time
        timeout_time = 120

    def get_view(view,disabled=False):
        view.clear_items()
        left_button = discord.ui.Button(
            style=discord.ButtonStyle.primary,
            emoji=emojis.left,
            row=1,
            disabled=True if current_page_index <= 0 else False
        )
        if disabled:
            left_button.disabled = True
        left_button.callback = lambda i: left_button_callback(i)
        view.add_item(left_button)

        stop_button = discord.ui.Button(
            style=discord.ButtonStyle.danger,
            emoji=emojis.stop,
            row=1
        )
        if disabled:
            stop_button.disabled = True
        stop_button.callback = lambda i: stop_button_callback(i)
        view.add_item(stop_button)


        right_button = discord.ui.Button(
            style=discord.ButtonStyle.primary,
            emoji=emojis.right,
            row=1,
            disabled=True if current_page_index >= len(content_data) - 1 else False
        )
        if disabled:
            right_button.disabled = True
        right_button.callback = lambda i: right_button_callback(i)
        view.add_item(right_button)
        
        return view

    def get_page(index):
        page = content_data[index]
        return page
    
    async def get_embed(page_index,embed):
        page = get_page(page_index)
        embed.clear_fields()
        for content in page:
            content_id = content[0]
            publisher = content[1]
            title = content[2]
            description = content[3]
            link = content[4]
            guild_id = content[5]
            datetime = content[6]
            reviews = content[7]
            reports = content[8]

            try:
                guild = await bot.get_guild(guild_id)
                guild_name = guild.name
            except:
                guild_name = "Not Found"
            
            embed.add_field(
                name=f"**{title}**",
                value=f"**• ID:** {content_id}\n**• Publisher:** <@{publisher}>\n**• Published:** <t:{int(datetime.timestamp())}>\n**• Guild:** {guild_name}\n**• Link:** {link}\n**• Reviews:** {len(reviews)}\n**• Reports:** {len(reports)}",
                inline=False
            )
            embed.set_footer(text=f"Page: {current_page_index + 1}/{len(content_data)}")
        return embed

    async def left_button_callback(interaction:discord.Interaction):
        if ctx.author != interaction.user:
            return await interaction.response.send_message(content=f"Only {ctx.author.mention} can use this button.",ephemeral=True)
        reset_timeout()
        nonlocal current_page_index,embed,view
        current_page_index -= 1
        embed = await get_embed(current_page_index,embed)
        view = get_view(view)
        await interaction.response.edit_message(embed=embed, view=view)
    
    async def right_button_callback(interaction:discord.Interaction):
        if ctx.author != interaction.user:
            return await interaction.response.send_message(content=f"Only {ctx.author.mention} can use this button.",ephemeral=True)
        reset_timeout()
        nonlocal current_page_index,embed,view
        current_page_index += 1
        embed = await get_embed(current_page_index,embed)
        view = get_view(view)
        await interaction.response.edit_message(embed=embed, view=view)
    
    async def stop_button_callback(interaction:discord.Interaction):
        if ctx.author != interaction.user:
            return await interaction.response.send_message(content=f"Only {ctx.author.mention} can use this button.",ephemeral=True)
        reset_timeout()
        nonlocal view,cancled
        view = get_view(view,disabled=True)
        await interaction.response.edit_message(view=view)
        cancled = True
    
    embed = await get_embed(current_page_index,embed)
    view = get_view(view)
    await message.edit(content=None,embed=embed, view=view)

    while True:
        timeout_time -= 1
        if timeout_time <= 0:
            view = get_view(view,disabled=True)
            await message.edit(view=view)
            break
        if cancled:
            break
        await asyncio.sleep(1)
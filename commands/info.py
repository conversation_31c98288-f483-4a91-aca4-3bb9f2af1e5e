import discord

from database import latency

async def info(ctx,bot):
    embed = discord.Embed(
        title="Bot Information",
        description=f"**Name:** `{bot.user.name}`\n**ID:** `{bot.user.id}`\n**Latency:** `{round(bot.latency*1000)}ms`\n**Database Latency:** `{(latency.get_latency_in_ms()):.1f}ms`\n\nDeveloped by [**AdnanBinPulok**](https://github.com/AdnanBinPulok) <@1058254151810830357>",
        color=discord.Color.green()
    )
    embed.set_thumbnail(url=bot.user.display_avatar.url)
    await ctx.send(embed=embed)
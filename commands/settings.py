import discord
import asyncio

from src.config import config
from data import emojis
from src import color

from database import guilds
from functions import create_data

from functions.checks import check_perms,check_bot_owner

async def settings(ctx,bot):
    if not check_perms(ctx) and not check_bot_owner(ctx.author.id) and ctx.author.id != ctx.guild.owner.id:
        return await ctx.send(f"**{emojis.error} You do not have permission to use this command.**")

    message = await ctx.send(f"{emojis.loading} Loading...")
    guild_id = ctx.guild.id


    def get_embed():
        guild_data = guilds.get_guild_data(guild_id)
        if not guild_data:
            guild_data = create_data.create_guild_data(guild_id)
        
        prefix = guild_data.get('prefix')
        main_menu_channel_id = guild_data.get('main_menu_channel')
        admins = guild_data.get('admin_ids')
        
        try:
            main_menu_channel = ctx.guild.get_channel(main_menu_channel_id)
            main_menu_channel = main_menu_channel.mention
        except:
            main_menu_channel = "Not Found"

        embed = discord.Embed(
            title="Settings",
            description="Here are the settings for this server.",
            color=color.blue
        )
        embed.add_field(
            name="Prefix",
            value=f"**• Prefix:** `{prefix}`",
            inline=True
        )
        embed.add_field(
            name="Main Menu Channel",
            value=f"**• Channel:** {main_menu_channel}",
            inline=True
        ) 
        embed.add_field(
            name="",
            value="",
            inline=False
        )
        embed.add_field(
            name="Admins",
            value=', '.join([f"<@{i}>" for i in admins]) if admins else 'No Admins',
            inline=False
        )
        embed.set_thumbnail(url=bot.user.display_avatar.url)
        embed.set_footer(text="Settings")
        return embed
    
    embed = get_embed()
    view = discord.ui.View(timeout=500)
    timeout_time = 120
    cancled = False
    def reset_timeout():
        nonlocal timeout_time
        timeout_time = 120

    def get_view(view,disabled=False):
        view.clear_items()

        set_prefix_button = discord.ui.Button(
            style=discord.ButtonStyle.primary,
            label="Set Prefix",
            emoji=emojis.prefix,
            row=1,
        )
        set_prefix_button.callback = lambda i: set_prefix_button_callback(i)
        if disabled:
            set_prefix_button.disabled = True
        view.add_item(set_prefix_button)

        admin_button = discord.ui.Button(
            style=discord.ButtonStyle.primary,
            label="Admins",
            emoji=emojis.admin,
            row=1,
        )
        admin_button.callback = lambda i: admin_button_callback(i)
        if disabled:
            admin_button.disabled = True
        view.add_item(admin_button)

        cancel_button = discord.ui.Button(
            style=discord.ButtonStyle.gray,
            label="Cancel",
            emoji=emojis.cancel,
            row=1,
        )
        cancel_button.callback = lambda i: cancel_button_callback(i)
        if disabled:
            cancel_button.disabled = True
        view.add_item(cancel_button)

        return view
    
    async def set_prefix_button_callback(interaction:discord.Interaction):
        if ctx.author != interaction.user:
            return await interaction.response.send_message(content=f"Only {ctx.author.mention} can use this button.",ephemeral=True)
        
        guild_data = guilds.get_guild_data(guild_id)
        if not guild_data:
            guild_data = create_data.create_guild_data(guild_id)
        
        old_prefix = guild_data.get('prefix')
        reset_timeout()

        class set_prefix_modal(discord.ui.Modal,title="Set Prefix"):
            new_prefix = discord.ui.TextInput(
                label="Prefix",
                placeholder="Prefix...",
                required=True,
                style=discord.TextStyle.short,
                default=old_prefix if old_prefix else config.prefix
            )
            async def on_submit(self,interaction:discord.Interaction):
                prefix = self.new_prefix.value
                if len(prefix) > 3:
                    return await interaction.response.send_message(content="Prefix can't be longer than 3 characters.",ephemeral=True)
                if len(prefix) < 1:
                    return await interaction.response.send_message(content="Prefix can't be empty.",ephemeral=True)
                reset_timeout()
                guild_data = guilds.get_guild_data(guild_id)
                if not guild_data:
                    guild_data = create_data.create_guild_data(guild_id)
                guild_data['prefix'] = prefix
                guilds.insert_guild_data(guild_id, guild_data)
                nonlocal view
                embed = get_embed()
                view = get_view(view)
                await interaction.response.edit_message(embed=embed, view=view)
                
        await interaction.response.send_modal(set_prefix_modal())

    async def admin_button_callback(interaction:discord.Interaction):
        if ctx.author != interaction.user:
            return await interaction.response.send_message(content=f"Only {ctx.author.mention} can use this button.",ephemeral=True)
        
        guild_data = guilds.get_guild_data(guild_id)
        if not guild_data:
            guild_data = create_data.create_guild_data(guild_id)
        
        old_admins = guild_data.get('admin_ids')
        reset_timeout()
        print(old_admins)
        class admin_modal(discord.ui.Modal,title="Admins"):
            admins = discord.ui.TextInput(
                label="Admins",
                placeholder="Place the admin ids here line by line...",
                required=True,
                style=discord.TextStyle.long,
                default='\n'.join([str(i) for i in old_admins]) if old_admins else ""
            )
            async def on_submit(self,interaction:discord.Interaction):
                admins = self.admins.value
                reset_timeout()
                guild_data = guilds.get_guild_data(guild_id)
                if not guild_data:
                    guild_data = create_data.create_guild_data(guild_id)
                
                admins = admins.split('\n')
                
                sorted_admins = []

                for admin in admins:
                    try:
                        admin = int(admin)
                        sorted_admins.append(admin)
                    except:
                        pass
                if ctx.author.id not in sorted_admins:
                    sorted_admins.append(ctx.author.id)
                print(sorted_admins)
                guild_data['admin_ids'] = sorted_admins
                guilds.insert_guild_data(guild_id, guild_data)
                nonlocal view
                embed = get_embed()
                view = get_view(view)
                await interaction.response.edit_message(embed=embed, view=view)
                
        await interaction.response.send_modal(admin_modal())
    
    async def cancel_button_callback(interaction:discord.Interaction):
        if ctx.author != interaction.user:
            return await interaction.response.send_message(content=f"Only {ctx.author.mention} can use this button.",ephemeral=True)
        reset_timeout()
        nonlocal view,cancled
        view = get_view(view,disabled=True)
        await interaction.response.edit_message(view=view)
        cancled = True
    
    view = get_view(view)

    message = await message.edit(content=None,embed=embed, view=view)

    while True:
        timeout_time -= 1
        if timeout_time <= 0:
            view = get_view(view,disabled=True)
            await message.edit(view=view)
            break
        if cancled:
            break
        await asyncio.sleep(1)
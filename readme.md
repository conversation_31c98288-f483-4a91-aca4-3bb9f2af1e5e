# Project Name

## Description

This project is a Discord bot that provides various functionalities such as setting up the bot, deleting content, listing all content, reloading the bot, showing bot settings, banning and unbanning users, and showing bot help.






## Installation

1. Clone the repository:
```
git clone <repository-url>
```
2. Install the required dependencies:
```
pip install -r requirements.txt
```
3. change the `config.json` file with your bot token and prefix and other settings.

## Usage

Run the bot with the following command:

```
python main.py
```

## Commands

- `setup`: Set up the bot. Usage: `!setup`
- `deletecontent`: Delete a content by content_id. Usage: `!deletecontent <content_id>`
- `listcontent`: List all content. Usage: `!listcontent`
- `reload`: Reload the bot. Usage: `!reload`
- `settings`: Show bot settings. Usage: `!settings`
- `banuser`: Ban a user. Usage: `!banuser <user> <reason>`
- `unbanuser`: Unban a user. Usage: `!unbanuser <user>`
- `help`: Show bot help. Usage: `!help`

## Contributing

Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.

